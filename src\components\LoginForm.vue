<template>
  <div id="signup-form">
    <form @submit.prevent="handleSubmit">
      <h2 class="title">登录账号</h2>
      <input type="email" placeholder="邮箱" v-model="email" />
      <input type="password" placeholder="密码" v-model="password" />
      <div class="error">{{ error }}</div>
      <button>登录</button>
    </form>
  </div>
</template>

<script setup>
import { useLogin } from '@/composables/useLogin';
import { ref } from 'vue';

const email = ref('')
const password = ref('')

const emit = defineEmits(['login'])

const { login, error } = useLogin()

const handleSubmit = async () => {
  await login(email.value, password.value)
  if (error.value) {
    error.value = "邮箱或密码不正确"
  }
  if (!error.value) {
    console.log('登录成功！')
    emit('login')
  }
}

</script>

<style scoped>
.title {
  font-size: 2rem;
  margin: 0 auto;
  margin-bottom: 1rem;

}

#signup-form {
  display: flex;
  justify-content: center;
  align-items: center;

}

form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  /* 宽度 */
  width: 30rem;
}

/* 输入框样式 */
input {
  font-size: 1rem;
  padding: 1.5rem;
  border: 1px solid var(--secondary-color);
  border-radius: 2rem;
  outline: none;
  transition: all 0.3s ease;
}

input:focus {
  border-color: var(--base-color-500);
}

/* 按钮样式 */
button {
  font-size: 1.5rem;
  padding: 0.5rem;
  border: none;
  border-radius: 0.5rem;
  background-color: var(--secondary-color-300);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover {
  background-color: var(--secondary-color-500);
}
</style>
