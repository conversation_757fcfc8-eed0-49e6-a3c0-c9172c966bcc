html {
  box-sizing: border-box;
}
*,
*::before,
*::after {
  box-sizing: inherit;
}
body {
  /* 设置页面的默认字体栈 */
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;

  /* 设置页面的默认基础字号 */
  font-size: 1rem; /* 16px */

  /* 设置页面的默认行高 */
  line-height: 1.6;

  /* 设置页面的默认文本颜色 */
  color: #333;

  /* 设置页面的默认背景颜色 */
  background-color: #fff;
}
/* 设置网站颜色 */
:root {
  --base-color: #42d392;
  --secondary-color: #647eff;

  --base-color-100: hsl(from var(--base-color) h s calc(l + 40));
  --base-color-200: hsl(from var(--base-color) h s calc(l + 30));
  --base-color-300: hsl(from var(--base-color) h s calc(l + 20));
  --base-color-400: hsl(from var(--base-color) h s calc(l + 10));
  --base-color-500: hsl(from var(--base-color) h s l);
  --base-color-600: hsl(from var(--base-color) h s calc(l - 10));
  --base-color-700: hsl(from var(--base-color) h s calc(l - 20));
  --base-color-800: hsl(from var(--base-color) h s calc(l - 30));
  --base-color-900: hsl(from var(--base-color) h s calc(l - 40));

  --base-light: hsl(from var(--base-color) h calc(s + 20) l);
  --base-accent: hsl(from var(--base-color) h calc(s - 20) l);

  --secondary-color-100: hsl(230, 100%, 90%);
  --secondary-color-200: hsl(230, 100%, 80%);
  --secondary-color-300: hsl(230, 100%, 70%);
  --secondary-color-400: hsl(230, 100%, 60%);
  --secondary-color-500: hsl(230, 100%, 50%);
  --secondary-color-600: hsl(230, 100%, 40%);
  --secondary-color-700: hsl(230, 100%, 30%);
  --secondary-color-800: hsl(230, 100%, 20%);
  --secondary-color-900: hsl(230, 100%, 10%);


  --secondary-light: hsl(from var(--secondary-color) h calc(s + 20) l);
  --secondary-accent: hsl(from var(--secondary-color) h calc(s - 20) l);

  --error-color: #f95656;

  

}

/* 错误信息样式 */
.error {
  color: var(--error-color);
  font-size: 1rem;
  margin: 0 auto;
  margin-bottom: 1rem;
}

/* 基础按钮样式 */
button {
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 2rem;
  background-color: var(--base-color-600);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover {
  background-color: var(--base-color-700);
}
